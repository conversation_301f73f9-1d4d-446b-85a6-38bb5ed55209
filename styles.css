/* Reset and Base Styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "Inter", sans-serif;
	background-color: #0a0a0a;
	color: #ffffff;
	line-height: 1.6;
	overflow-x: hidden;
	max-width: 1440px;
	margin: 0 auto;
}

/* Container */
.container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
}

/* Navigation */
.navbar {
	position: fixed;
	top: 0;
	width: 100%;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(10px);
	z-index: 1000;
	padding: 20px 0;
}

.nav-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.logo-text {
	color: #ffffff;
	font-size: 20px;
	font-weight: 600;
}

.nav-menu {
	display: flex;
	gap: 40px;
	align-items: center;
}

.nav-link {
	color: #ffffff;
	text-decoration: none;
	font-weight: 400;
	font-size: 16px;
	transition: color 0.3s ease;
}

.nav-link:hover {
	color: #dc2626;
}

.nav-cta {
	display: flex;
	align-items: center;
}

.join-presale-btn {
	background: transparent;
	color: #ffffff;
	border: 1px solid #ffffff;
	padding: 10px 20px;
	font-size: 14px;
	font-weight: 500;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.join-presale-btn:hover {
	background: #ffffff;
	color: #000000;
}

/* Hero Section */
.hero {
	display: flex;
	align-items: center;
	background-image: url("assets/backgrounds/bg.png");
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	position: relative;
	overflow: hidden;
	aspect-ratio: 8/5;
	width: 100%;
	max-width: 1440px;
}

.hero-container {
	padding: 0 80px;
	display: flex;
	align-items: center;
	height: 100%;
	width: 100%;
}

.hero-content {
	max-width: 600px;
	z-index: 2;
}

.hero-title {
	font-size: 72px;
	font-weight: 700;
	line-height: 1.1;
	margin-bottom: 32px;
	color: #ffffff;
}

.hero-description {
	font-size: 18px;
	color: #ffffff;
	margin-bottom: 40px;
	line-height: 1.6;
	opacity: 0.9;
}

.hero-buttons {
	display: flex;
	gap: 16px;
	align-items: center;
}

.join-presale-button {
	background: #dc2626;
	color: #ffffff;
	border: none;
	padding: 16px 32px;
	font-size: 16px;
	font-weight: 600;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.join-presale-button:hover {
	background: #b91c1c;
	transform: translateY(-2px);
}

.learn-more-button {
	background: transparent;
	color: #ffffff;
	border: 1px solid #ffffff;
	padding: 16px 32px;
	font-size: 16px;
	font-weight: 600;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.learn-more-button:hover {
	background: #ffffff;
	color: #000000;
}

/* Statistics Section */
.stats {
	padding: 80px 0;
	background: #111111;
}

.stats-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 80px;
	text-align: center;
}

.stat-number {
	font-size: 48px;
	font-weight: 900;
	color: #dc2626;
	margin-bottom: 8px;
}

.stat-label {
	font-size: 16px;
	color: #a1a1aa;
	font-weight: 500;
}

/* Section Styles */
.section-title {
	font-size: 56px;
	font-weight: 800;
	line-height: 1.2;
	margin-bottom: 24px;
}

.section-description {
	font-size: 18px;
	color: #a1a1aa;
	line-height: 1.7;
	margin-bottom: 40px;
}

.red-text {
	color: #dc2626;
}

/* Dedicated Section */
.dedicated {
	padding: 120px 0;
	background: #0a0a0a;
}

.dedicated-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
}

.section-image {
	width: 100%;
	height: auto;
	border-radius: 12px;
}

/* Ecosystem Section */
.ecosystem {
	padding: 120px 0;
	background: #111111;
}

.ecosystem-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	text-align: center;
}

.ecosystem-title {
	font-size: 56px;
	font-weight: 800;
	margin-bottom: 80px;
}

.ecosystem-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 40px;
}

.ecosystem-card {
	background: #1a1a1a;
	padding: 40px;
	border-radius: 16px;
	text-align: left;
	transition: transform 0.3s ease;
}

.ecosystem-card:hover {
	transform: translateY(-8px);
}

.card-icon {
	width: 64px;
	height: 64px;
	background: #dc2626;
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 24px;
}

.icon {
	width: 32px;
	height: 32px;
}

.card-title {
	font-size: 24px;
	font-weight: 700;
	margin-bottom: 16px;
	color: #ffffff;
}

.card-description {
	font-size: 16px;
	color: #a1a1aa;
	line-height: 1.6;
}

/* Power Section */
.power {
	padding: 120px 0;
	background: #0a0a0a;
}

.power-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
}

.power-features {
	margin-bottom: 40px;
}

.feature-item {
	display: flex;
	gap: 20px;
	margin-bottom: 32px;
}

.feature-icon {
	width: 48px;
	height: 48px;
	background: #dc2626;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.feature-icon-img {
	width: 24px;
	height: 24px;
}

.feature-title {
	font-size: 18px;
	font-weight: 600;
	margin-bottom: 8px;
	color: #ffffff;
}

.feature-description {
	font-size: 14px;
	color: #a1a1aa;
}

/* Marketplace Section */
.marketplace {
	padding: 120px 0;
	background: #111111;
	text-align: center;
}

.marketplace-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
}

.marketplace-features {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 60px;
	margin-top: 80px;
}

.marketplace-feature {
	text-align: left;
}

.feature-number {
	font-size: 48px;
	font-weight: 900;
	color: #dc2626;
	margin-bottom: 16px;
}

.feature-title {
	font-size: 24px;
	font-weight: 700;
	margin-bottom: 16px;
	color: #ffffff;
}

.feature-description {
	font-size: 16px;
	color: #a1a1aa;
	line-height: 1.6;
}

/* BRRS Section */
.brrs {
	padding: 120px 0;
	background: #0a0a0a;
}

.brrs-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	align-items: center;
}

.brrs-stats {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 40px;
	margin: 40px 0;
}

.brrs-stat {
	text-align: center;
}

/* Leaders Section */
.leaders {
	padding: 120px 0;
	background: #111111;
}

.leaders-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
	text-align: center;
}

.leaders-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 40px;
	margin-top: 80px;
}

.leader-card {
	background: #1a1a1a;
	padding: 40px;
	border-radius: 16px;
	display: flex;
	gap: 24px;
	text-align: left;
	transition: transform 0.3s ease;
}

.leader-card:hover {
	transform: translateY(-4px);
}

.leader-image {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	object-fit: cover;
	flex-shrink: 0;
}

.leader-name {
	font-size: 20px;
	font-weight: 700;
	margin-bottom: 8px;
	color: #ffffff;
}

.leader-title {
	font-size: 16px;
	font-weight: 600;
	color: #dc2626;
	margin-bottom: 12px;
}

.leader-description {
	font-size: 14px;
	color: #a1a1aa;
	line-height: 1.5;
}

/* Footer */
.footer {
	background: #0a0a0a;
	padding: 80px 0 40px;
	border-top: 1px solid #1a1a1a;
}

.footer-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 80px;
}

.footer-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 80px;
	margin-bottom: 60px;
}

.footer-title {
	font-size: 40px;
	font-weight: 800;
	line-height: 1.2;
	margin-bottom: 24px;
}

.footer-description {
	font-size: 16px;
	color: #a1a1aa;
	line-height: 1.6;
	margin-bottom: 32px;
}

.footer-email {
	display: flex;
	gap: 12px;
}

.email-input {
	flex: 1;
	background: #1a1a1a;
	border: 1px solid #333333;
	border-radius: 8px;
	padding: 12px 16px;
	color: #ffffff;
	font-size: 14px;
}

.email-input::placeholder {
	color: #666666;
}

.email-submit {
	background: #dc2626;
	color: #ffffff;
	border: none;
	border-radius: 8px;
	padding: 12px 24px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.email-submit:hover {
	background: #b91c1c;
}

.footer-links {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 40px;
}

.footer-column-title {
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 20px;
	color: #ffffff;
}

.footer-link {
	display: block;
	color: #a1a1aa;
	text-decoration: none;
	font-size: 14px;
	margin-bottom: 12px;
	transition: color 0.3s ease;
}

.footer-link:hover {
	color: #dc2626;
}

.footer-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 40px;
	border-top: 1px solid #1a1a1a;
}

.footer-bottom-left {
	display: flex;
	align-items: center;
	gap: 20px;
}

.footer-logo {
	height: 32px;
}

.footer-copyright {
	font-size: 14px;
	color: #a1a1aa;
}

.social-links {
	display: flex;
	gap: 16px;
}

.social-link {
	width: 40px;
	height: 40px;
	background: #1a1a1a;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: background-color 0.3s ease;
}

.social-link:hover {
	background: #dc2626;
}

.social-icon {
	width: 20px;
	height: 20px;
}

/* Responsive Design */
@media (max-width: 1440px) {
	body {
		max-width: 100%;
	}
}

@media (max-width: 1200px) {
	.container,
	.nav-container,
	.hero-container,
	.stats-container,
	.dedicated-container,
	.ecosystem-container,
	.power-container,
	.marketplace-container,
	.brrs-container,
	.leaders-container,
	.footer-container {
		padding: 0 40px;
	}
}

@media (max-width: 768px) {
	.nav-container {
		flex-direction: column;
		gap: 20px;
	}

	.nav-menu {
		gap: 20px;
	}

	.hero {
		aspect-ratio: 4/3;
		min-height: 500px;
	}

	.hero-buttons {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.join-presale-button,
	.learn-more-button {
		width: 100%;
		text-align: center;
	}

	.dedicated-container,
	.power-container,
	.brrs-container,
	.footer-content {
		grid-template-columns: 1fr;
		gap: 40px;
	}

	.ecosystem-grid,
	.marketplace-features,
	.leaders-grid {
		grid-template-columns: 1fr;
	}

	.stats-container,
	.brrs-stats {
		grid-template-columns: 1fr;
		gap: 40px;
	}

	.hero-title {
		font-size: 48px;
	}

	.section-title {
		font-size: 40px;
	}

	.ecosystem-title {
		font-size: 40px;
	}
}
